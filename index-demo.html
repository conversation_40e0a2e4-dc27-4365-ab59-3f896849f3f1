<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式Vue页面演示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .index-container {
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e0e0e0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }

        .header-mobile .header-content {
            height: 60px;
            padding: 0 16px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-img {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .logo-text {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }

        .nav-desktop {
            display: flex;
            gap: 32px;
        }

        .nav-link {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-link:hover,
        .nav-link.active {
            color: #3498db;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            right: 0;
            height: 2px;
            background: #3498db;
        }

        /* 移动端菜单按钮 */
        .menu-toggle {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            width: 30px;
            height: 30px;
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 0;
        }

        .menu-toggle span {
            width: 100%;
            height: 3px;
            background: #333;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(8px, 8px);
        }

        .menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        /* 移动端导航菜单 */
        .nav-mobile {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 主要内容区域 */
        .main-content {
            margin-top: 70px;
        }

        /* Hero区域 */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            min-height: 600px;
            display: flex;
            align-items: center;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .hero-mobile {
            grid-template-columns: 1fr;
            text-align: center;
            gap: 40px;
            padding: 0 16px;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-secondary:hover {
            background: white;
            color: #667eea;
        }

        .hero-image {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 功能特色区域 */
        .features-section {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 60px;
            color: #2c3e50;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 40px;
        }

        .features-mobile {
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 32px;
            color: white;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 16px;
            color: #2c3e50;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            z-index: 999;
        }

        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .main-content {
                margin-top: 60px;
            }
            
            .hero-section {
                padding: 60px 0;
                min-height: 500px;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .hero-buttons {
                justify-content: center;
            }
            
            .btn {
                padding: 12px 24px;
                font-size: 14px;
            }
            
            .section-title {
                font-size: 2rem;
                margin-bottom: 40px;
            }
            
            .features-section {
                padding: 60px 0;
            }
            
            .feature-card {
                padding: 30px 20px;
            }
            
            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
            
            .feature-title {
                font-size: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 280px;
            }
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <div id="app"></div>

    <script>
        const { createApp, ref, onMounted, onUnmounted } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isMobile = ref(false);
                const showMobileMenu = ref(false);
                const showBackToTop = ref(false);

                // 功能数据
                const features = ref([
                    {
                        id: 1,
                        icon: 'fas fa-rocket',
                        title: '高效便捷',
                        description: '简化操作流程，提升工作效率'
                    },
                    {
                        id: 2,
                        icon: 'fas fa-shield-alt',
                        title: '安全可靠',
                        description: '多重安全保障，保护数据安全'
                    },
                    {
                        id: 3,
                        icon: 'fas fa-cogs',
                        title: '智能化',
                        description: 'AI驱动的智能化解决方案'
                    },
                    {
                        id: 4,
                        icon: 'fas fa-users',
                        title: '团队协作',
                        description: '支持多人协作，提升团队效率'
                    }
                ]);

                // 检测设备类型
                const checkDevice = () => {
                    isMobile.value = window.innerWidth <= 768;
                };

                // 处理窗口大小变化
                const handleResize = () => {
                    checkDevice();
                    if (!isMobile.value) {
                        showMobileMenu.value = false;
                    }
                };

                // 处理滚动事件
                const handleScroll = () => {
                    showBackToTop.value = window.scrollY > 300;
                };

                // 切换移动端菜单
                const toggleMobileMenu = () => {
                    showMobileMenu.value = !showMobileMenu.value;
                };

                // 关闭移动端菜单
                const closeMobileMenu = () => {
                    showMobileMenu.value = false;
                };

                // 返回顶部
                const scrollToTop = () => {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                };

                // 事件处理函数
                const handleGetStarted = () => {
                    alert('开始使用功能');
                };

                const handleLearnMore = () => {
                    alert('了解更多信息');
                };

                const handleFeatureClick = (feature) => {
                    alert(`点击了功能: ${feature.title}`);
                };

                // 生命周期
                onMounted(() => {
                    checkDevice();
                    window.addEventListener('resize', handleResize);
                    window.addEventListener('scroll', handleScroll);
                });

                onUnmounted(() => {
                    window.removeEventListener('resize', handleResize);
                    window.removeEventListener('scroll', handleScroll);
                });

                return {
                    isMobile,
                    showMobileMenu,
                    showBackToTop,
                    features,
                    toggleMobileMenu,
                    closeMobileMenu,
                    scrollToTop,
                    handleGetStarted,
                    handleLearnMore,
                    handleFeatureClick
                };
            },
            template: `
                <div class="index-container">
                    <!-- 顶部导航栏 -->
                    <header class="header" :class="{ 'header-mobile': isMobile }">
                        <div class="header-content">
                            <div class="logo">
                                <div class="logo-img">Logo</div>
                                <span class="logo-text">智慧平台</span>
                            </div>
                            
                            <!-- 桌面端导航 -->
                            <nav class="nav-desktop" v-if="!isMobile">
                                <a href="#home" class="nav-link active">首页</a>
                                <a href="#features" class="nav-link">功能</a>
                                <a href="#about" class="nav-link">关于</a>
                                <a href="#contact" class="nav-link">联系</a>
                            </nav>
                            
                            <!-- 移动端菜单按钮 -->
                            <button 
                                class="menu-toggle" 
                                v-if="isMobile"
                                @click="toggleMobileMenu"
                                :class="{ 'active': showMobileMenu }"
                            >
                                <span></span>
                                <span></span>
                                <span></span>
                            </button>
                        </div>
                        
                        <!-- 移动端导航菜单 -->
                        <nav class="nav-mobile" v-if="isMobile && showMobileMenu">
                            <a href="#home" class="nav-link" @click="closeMobileMenu">首页</a>
                            <a href="#features" class="nav-link" @click="closeMobileMenu">功能</a>
                            <a href="#about" class="nav-link" @click="closeMobileMenu">关于</a>
                            <a href="#contact" class="nav-link" @click="closeMobileMenu">联系</a>
                        </nav>
                    </header>

                    <!-- 主要内容区域 -->
                    <main class="main-content">
                        <!-- Hero区域 -->
                        <section class="hero-section" id="home">
                            <div class="hero-content" :class="{ 'hero-mobile': isMobile }">
                                <div class="hero-text">
                                    <h1 class="hero-title">欢迎使用智慧平台</h1>
                                    <p class="hero-subtitle">为您提供高效、便捷的数字化解决方案</p>
                                    <div class="hero-buttons">
                                        <button class="btn btn-primary" @click="handleGetStarted">立即开始</button>
                                        <button class="btn btn-secondary" @click="handleLearnMore">了解更多</button>
                                    </div>
                                </div>
                                <div class="hero-image" v-if="!isMobile">
                                    <i class="fas fa-laptop-code"></i>
                                </div>
                            </div>
                        </section>

                        <!-- 功能特色区域 -->
                        <section class="features-section" id="features">
                            <div class="section-container">
                                <h2 class="section-title">核心功能</h2>
                                <div class="features-grid" :class="{ 'features-mobile': isMobile }">
                                    <div 
                                        class="feature-card" 
                                        v-for="feature in features" 
                                        :key="feature.id"
                                        @click="handleFeatureClick(feature)"
                                    >
                                        <div class="feature-icon">
                                            <i :class="feature.icon"></i>
                                        </div>
                                        <h3 class="feature-title">{{ feature.title }}</h3>
                                        <p class="feature-description">{{ feature.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </main>

                    <!-- 返回顶部按钮 -->
                    <button 
                        class="back-to-top" 
                        v-show="showBackToTop"
                        @click="scrollToTop"
                    >
                        <i class="fas fa-arrow-up"></i>
                    </button>
                </div>
            `
        }).mount('#app');
    </script>
</body>
</html>
