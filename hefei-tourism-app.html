<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合肥市智慧文旅助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #2E86AB 0%, #A23B72 50%, #F18F01 100%);
            color: white;
            padding: 20px 16px 16px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        /* Banner Section */
        .banner-section {
            position: relative;
            height: 200px;
            overflow: hidden;
            margin: 16px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .banner-slide {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><rect fill="%23A23B72" width="400" height="200"/><text x="200" y="100" text-anchor="middle" fill="white" font-size="24">包公园</text></svg>');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 20px;
            color: white;
            transition: opacity 0.5s ease;
        }

        .banner-slide.active {
            opacity: 1;
        }

        .banner-slide:not(.active) {
            opacity: 0;
        }

        .banner-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .banner-desc {
            font-size: 14px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .banner-btn {
            background: #F18F01;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            align-self: flex-start;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .banner-btn:hover {
            background: #e67e00;
        }

        .banner-indicators {
            position: absolute;
            bottom: 12px;
            right: 16px;
            display: flex;
            gap: 6px;
        }

        .indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .indicator.active {
            background: white;
        }

        /* Overview Section */
        .overview-section {
            margin: 16px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2E86AB;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .map-container {
            height: 180px;
            background: linear-gradient(45deg, #e8f5e8 0%, #f0f8ff 100%);
            border-radius: 8px;
            position: relative;
            margin-bottom: 12px;
            overflow: hidden;
        }

        .map-point {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #F18F01;
            border-radius: 50%;
            border: 2px solid white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            transition: transform 0.2s ease;
        }

        .map-point:hover {
            transform: scale(1.2);
        }

        .map-point.nature { background: #28a745; top: 30%; left: 20%; }
        .map-point.culture { background: #A23B72; top: 50%; left: 60%; }
        .map-point.tech { background: #2E86AB; top: 70%; left: 40%; }

        .overview-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 16px;
            margin: 16px;
        }

        .quick-action {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .quick-action:hover {
            transform: translateY(-2px);
        }

        .quick-action-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #F18F01, #e67e00);
            border-radius: 50%;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .quick-action-text {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }

        /* Nearby Section */
        .nearby-section {
            margin: 16px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .nearby-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .filter-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            color: #666;
        }

        .nearby-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .nearby-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.2s ease;
            cursor: pointer;
        }

        .nearby-item:hover {
            background: #f8f9fa;
        }

        .nearby-icon {
            width: 40px;
            height: 40px;
            background: #2E86AB;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .nearby-info {
            flex: 1;
        }

        .nearby-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .nearby-meta {
            font-size: 12px;
            color: #666;
        }

        /* AI Assistant */
        .ai-assistant {
            position: fixed;
            bottom: 80px;
            right: 16px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #F18F01, #e67e00);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(241, 143, 1, 0.3);
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .ai-assistant:hover {
            transform: scale(1.1);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            padding: 8px 0;
            z-index: 999;
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .nav-item.active {
            color: #F18F01;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
        }

        /* Hot Attractions */
        .hot-attractions-section {
            margin: 16px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .attractions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .attraction-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            position: relative;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .attraction-card:hover {
            transform: translateY(-2px);
        }

        .attraction-image {
            font-size: 32px;
            text-align: center;
            margin-bottom: 8px;
        }

        .attraction-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .attraction-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .attraction-price {
            font-size: 12px;
            color: #F18F01;
            font-weight: 500;
        }

        .favorite-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .favorite-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .favorite-btn.active {
            color: #e74c3c;
        }

        /* Activities */
        .activities-section {
            margin: 16px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .activities-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .view-all-btn {
            font-size: 12px;
            color: #F18F01;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .view-all-btn:hover {
            background: #fff3e0;
        }

        .activities-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .activities-scroll::-webkit-scrollbar {
            height: 4px;
        }

        .activities-scroll::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .activities-scroll::-webkit-scrollbar-thumb {
            background: #F18F01;
            border-radius: 2px;
        }

        .activity-card {
            min-width: 200px;
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .activity-card:hover {
            transform: translateY(-2px);
        }

        .activity-image {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .activity-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .activity-time, .activity-location {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .activity-status {
            font-size: 12px;
            color: #F18F01;
            font-weight: 500;
        }

        /* Travel Guides */
        .guides-section {
            margin: 16px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .guides-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .guide-item {
            display: flex;
            gap: 12px;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .guide-item:hover {
            background: #f8f9fa;
        }

        .guide-image {
            width: 48px;
            height: 48px;
            background: #e3f2fd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .guide-content {
            flex: 1;
        }

        .guide-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .guide-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #666;
        }

        /* AI Chat Modal */
        .ai-chat-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            height: 60%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            z-index: 1001;
            display: none;
            flex-direction: column;
        }

        .ai-chat-modal.active {
            display: flex;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
        }

        .chat-title {
            font-size: 16px;
            font-weight: 600;
        }

        .chat-close {
            width: 32px;
            height: 32px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
        }

        .chat-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .chat-message {
            margin-bottom: 16px;
        }

        .chat-message.bot .message-text {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.4;
        }

        .common-questions {
            margin-top: 16px;
        }

        .question-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #666;
        }

        .question-item {
            background: #e3f2fd;
            padding: 8px 12px;
            border-radius: 16px;
            margin-bottom: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .question-item:hover {
            background: #bbdefb;
        }

        .chat-input {
            display: flex;
            padding: 16px;
            border-top: 1px solid #e9ecef;
            gap: 8px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }

        .chat-input button {
            background: #F18F01;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }

        /* Weather & Tips */
        .weather-tips-section {
            margin: 16px;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .weather-card {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .weather-icon {
            font-size: 32px;
        }

        .weather-info {
            flex: 1;
        }

        .weather-temp {
            font-size: 20px;
            font-weight: 600;
            color: #2E86AB;
        }

        .weather-desc {
            font-size: 14px;
            color: #666;
        }

        .weather-tips {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .tip-item {
            font-size: 12px;
            color: #666;
        }

        /* Quick Services */
        .quick-services-section {
            margin: 16px;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }

        .service-item {
            text-align: center;
            padding: 12px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .service-item:hover {
            background: #f8f9fa;
        }

        .service-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .service-text {
            font-size: 12px;
            color: #333;
        }

        /* Footer */
        .footer-info {
            margin: 16px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px 16px;
            margin-bottom: 80px;
        }

        .footer-content {
            text-align: center;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 16px;
        }

        .logo-icon {
            font-size: 24px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #2E86AB;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 12px;
        }

        .link-item {
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .link-item:hover {
            color: #F18F01;
        }

        .footer-copyright {
            font-size: 12px;
            color: #999;
        }

        /* Floating Action Button */
        .fab-container {
            position: fixed;
            bottom: 150px;
            right: 16px;
            z-index: 1000;
        }

        .fab-main {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #A23B72, #8e2a5e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(162, 59, 114, 0.3);
            transition: all 0.3s ease;
        }

        .fab-main:hover {
            transform: scale(1.1);
        }

        .fab-main.active {
            transform: rotate(45deg);
        }

        .fab-options {
            position: absolute;
            bottom: 70px;
            right: 0;
            display: flex;
            flex-direction: column;
            gap: 12px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .fab-options.active {
            opacity: 1;
            visibility: visible;
            bottom: 70px;
        }

        .fab-option {
            width: 48px;
            height: 48px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            transform: scale(0);
        }

        .fab-options.active .fab-option {
            transform: scale(1);
        }

        .fab-options.active .fab-option:nth-child(1) {
            transition-delay: 0.1s;
        }

        .fab-options.active .fab-option:nth-child(2) {
            transition-delay: 0.2s;
        }

        .fab-options.active .fab-option:nth-child(3) {
            transition-delay: 0.3s;
        }

        .fab-option:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 375px) {
            .container {
                max-width: 375px;
            }
            
            .header {
                padding: 16px 12px 12px;
            }
            
            .banner-section {
                margin: 12px;
                height: 180px;
            }
            
            .quick-actions {
                margin: 12px;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>合肥市智慧文旅助手</h1>
            <div class="subtitle">发现合肥之美，智享文旅体验</div>
        </div>

        <!-- Banner Section -->
        <div class="banner-section">
            <div class="banner-slide active">
                <div class="banner-title">包公园</div>
                <div class="banner-desc">感受北宋名臣文化，探寻清官廉政精神</div>
                <button class="banner-btn" onclick="viewAttraction('baogong')">立即查看</button>
            </div>
            <div class="banner-slide">
                <div class="banner-title">黄山</div>
                <div class="banner-desc">奇松怪石云海温泉，天下第一奇山</div>
                <button class="banner-btn" onclick="viewAttraction('huangshan')">立即查看</button>
            </div>
            <div class="banner-slide">
                <div class="banner-title">三河古镇</div>
                <div class="banner-desc">千年古镇风韵，江南水乡情怀</div>
                <button class="banner-btn" onclick="viewAttraction('sanhe')">立即查看</button>
            </div>
            <div class="banner-indicators">
                <div class="indicator active" onclick="showSlide(0)"></div>
                <div class="indicator" onclick="showSlide(1)"></div>
                <div class="indicator" onclick="showSlide(2)"></div>
            </div>
        </div>

        <!-- Overview Section -->
        <div class="overview-section">
            <div class="section-title">
                🗺️ 合肥概览
            </div>
            <div class="map-container">
                <div class="map-point nature" onclick="showAttractionInfo('自然景观', '黄山、大蜀山等')" title="自然景观">🏔️</div>
                <div class="map-point culture" onclick="showAttractionInfo('人文历史', '包公园、李鸿章故居等')" title="人文历史">🏛️</div>
                <div class="map-point tech" onclick="showAttractionInfo('科创景点', '科技馆、创新馆等')" title="科创景点">🔬</div>
            </div>
            <div class="overview-text">
                合肥市总面积11,445平方公里，拥有4A级以上景区30余个，年接待游客超过1.2亿人次。这里既有深厚的历史文化底蕴，又有现代科技创新活力。
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="quick-action" onclick="planTrip()">
                <div class="quick-action-icon">🗺️</div>
                <div class="quick-action-text">行程规划</div>
            </div>
            <div class="quick-action" onclick="recognizeImage()">
                <div class="quick-action-icon">📷</div>
                <div class="quick-action-text">看图识景</div>
            </div>
        </div>

        <!-- Nearby Section -->
        <div class="nearby-section">
            <div class="nearby-header">
                <div class="section-title">📍 附近推荐</div>
                <button class="filter-btn" onclick="showFilters()">筛选</button>
            </div>
            <div class="nearby-list">
                <div class="nearby-item" onclick="viewAttraction('baogong')">
                    <div class="nearby-icon">🏛️</div>
                    <div class="nearby-info">
                        <div class="nearby-name">包公园</div>
                        <div class="nearby-meta">1.2km · ⭐4.6 · 免费开放</div>
                    </div>
                </div>
                <div class="nearby-item" onclick="viewAttraction('science')">
                    <div class="nearby-icon">🔬</div>
                    <div class="nearby-info">
                        <div class="nearby-name">合肥科技馆</div>
                        <div class="nearby-meta">2.1km · ⭐4.5 · ¥30起</div>
                    </div>
                </div>
                <div class="nearby-item" onclick="viewAttraction('luogang')">
                    <div class="nearby-icon">🌳</div>
                    <div class="nearby-info">
                        <div class="nearby-name">骆岗公园</div>
                        <div class="nearby-meta">3.5km · ⭐4.8 · 免费开放</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hot Attractions Section -->
        <div class="hot-attractions-section">
            <div class="section-title">
                🔥 热门景点
            </div>
            <div class="attractions-grid">
                <div class="attraction-card" onclick="viewAttraction('baogong')">
                    <div class="attraction-image">🏛️</div>
                    <div class="attraction-info">
                        <div class="attraction-name">包公园</div>
                        <div class="attraction-meta">1.2km · ⭐4.6</div>
                        <div class="attraction-price">免费</div>
                    </div>
                    <div class="favorite-btn" onclick="toggleFavorite(event, 'baogong')">♡</div>
                </div>
                <div class="attraction-card" onclick="viewAttraction('science')">
                    <div class="attraction-image">🔬</div>
                    <div class="attraction-info">
                        <div class="attraction-name">科技馆</div>
                        <div class="attraction-meta">2.1km · ⭐4.5</div>
                        <div class="attraction-price">¥30起</div>
                    </div>
                    <div class="favorite-btn" onclick="toggleFavorite(event, 'science')">♡</div>
                </div>
                <div class="attraction-card" onclick="viewAttraction('luogang')">
                    <div class="attraction-image">🌳</div>
                    <div class="attraction-info">
                        <div class="attraction-name">骆岗公园</div>
                        <div class="attraction-meta">3.5km · ⭐4.8</div>
                        <div class="attraction-price">免费</div>
                    </div>
                    <div class="favorite-btn" onclick="toggleFavorite(event, 'luogang')">♡</div>
                </div>
                <div class="attraction-card" onclick="viewAttraction('innovation')">
                    <div class="attraction-image">💡</div>
                    <div class="attraction-info">
                        <div class="attraction-name">创新馆</div>
                        <div class="attraction-meta">4.2km · ⭐4.4</div>
                        <div class="attraction-price">¥20起</div>
                    </div>
                    <div class="favorite-btn" onclick="toggleFavorite(event, 'innovation')">♡</div>
                </div>
            </div>
        </div>

        <!-- Activities Section -->
        <div class="activities-section">
            <div class="activities-header">
                <div class="section-title">🎉 推荐活动</div>
                <div class="view-all-btn" onclick="viewAllActivities()">查看全部</div>
            </div>
            <div class="activities-scroll">
                <div class="activity-card" onclick="viewActivity('heritage')">
                    <div class="activity-image">🎭</div>
                    <div class="activity-info">
                        <div class="activity-name">2025合肥非遗文化节</div>
                        <div class="activity-time">7.15-7.20</div>
                        <div class="activity-location">明教寺</div>
                        <div class="activity-status">剩50席</div>
                    </div>
                </div>
                <div class="activity-card" onclick="viewActivity('camping')">
                    <div class="activity-image">🏕️</div>
                    <div class="activity-info">
                        <div class="activity-name">夏日露营节</div>
                        <div class="activity-time">7.20-7.22</div>
                        <div class="activity-location">骆岗公园</div>
                        <div class="activity-status">火热报名中</div>
                    </div>
                </div>
                <div class="activity-card" onclick="viewActivity('tech')">
                    <div class="activity-image">🚀</div>
                    <div class="activity-info">
                        <div class="activity-name">科技体验周</div>
                        <div class="activity-time">7.25-7.31</div>
                        <div class="activity-location">科技馆</div>
                        <div class="activity-status">免费参与</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Travel Guides Section -->
        <div class="guides-section">
            <div class="section-title">📖 旅游攻略</div>
            <div class="guides-list">
                <div class="guide-item" onclick="viewGuide('family')">
                    <div class="guide-image">👨‍👩‍👧‍👦</div>
                    <div class="guide-content">
                        <div class="guide-title">亲子游必看！合肥3天2晚遛娃攻略</div>
                        <div class="guide-meta">
                            <span class="guide-author">旅游达人小王</span>
                            <span class="guide-views">1.2万阅读</span>
                        </div>
                    </div>
                </div>
                <div class="guide-item" onclick="viewGuide('food')">
                    <div class="guide-image">🍜</div>
                    <div class="guide-content">
                        <div class="guide-title">合肥美食地图：从街头小吃到徽菜大餐</div>
                        <div class="guide-meta">
                            <span class="guide-author">美食探索家</span>
                            <span class="guide-views">8.5k阅读</span>
                        </div>
                    </div>
                </div>
                <div class="guide-item" onclick="viewGuide('weekend')">
                    <div class="guide-image">🌅</div>
                    <div class="guide-content">
                        <div class="guide-title">周末好去处：合肥一日游完美路线</div>
                        <div class="guide-meta">
                            <span class="guide-author">本地向导</span>
                            <span class="guide-views">6.8k阅读</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weather & Tips Section -->
        <div class="weather-tips-section">
            <div class="weather-card">
                <div class="weather-icon">☀️</div>
                <div class="weather-info">
                    <div class="weather-temp">28°C</div>
                    <div class="weather-desc">晴朗 · 适宜出游</div>
                </div>
                <div class="weather-tips">
                    <div class="tip-item">🧴 建议携带防晒霜</div>
                    <div class="tip-item">💧 记得多喝水</div>
                </div>
            </div>
        </div>

        <!-- Quick Services -->
        <div class="quick-services-section">
            <div class="section-title">🛎️ 便民服务</div>
            <div class="services-grid">
                <div class="service-item" onclick="openService('parking')">
                    <div class="service-icon">🅿️</div>
                    <div class="service-text">停车查询</div>
                </div>
                <div class="service-item" onclick="openService('toilet')">
                    <div class="service-icon">🚻</div>
                    <div class="service-text">卫生间</div>
                </div>
                <div class="service-item" onclick="openService('medical')">
                    <div class="service-icon">🏥</div>
                    <div class="service-text">医疗服务</div>
                </div>
                <div class="service-item" onclick="openService('lost')">
                    <div class="service-icon">📞</div>
                    <div class="service-text">失物招领</div>
                </div>
            </div>
        </div>

        <!-- Footer Info -->
        <div class="footer-info">
            <div class="footer-content">
                <div class="footer-logo">
                    <div class="logo-icon">🏛️</div>
                    <div class="logo-text">合肥文旅</div>
                </div>
                <div class="footer-links">
                    <div class="link-item" onclick="openPage('about')">关于我们</div>
                    <div class="link-item" onclick="openPage('contact')">联系我们</div>
                    <div class="link-item" onclick="openPage('feedback')">意见反馈</div>
                </div>
                <div class="footer-copyright">
                    © 2025 合肥市文化和旅游局 版权所有
                </div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <div class="fab-container">
            <div class="fab-main" onclick="toggleFAB()">
                <span id="fabIcon">+</span>
            </div>
            <div class="fab-options" id="fabOptions">
                <div class="fab-option" onclick="quickAction('emergency')" title="紧急求助">
                    <span>🆘</span>
                </div>
                <div class="fab-option" onclick="quickAction('translate')" title="语言翻译">
                    <span>🌐</span>
                </div>
                <div class="fab-option" onclick="quickAction('accessibility')" title="无障碍服务">
                    <span>♿</span>
                </div>
            </div>
        </div>

        <!-- AI Assistant -->
        <div class="ai-assistant" onclick="openAIChat()">
            🤖
        </div>

        <!-- AI Chat Modal -->
        <div class="ai-chat-modal" id="aiChatModal">
            <div class="chat-header">
                <div class="chat-title">🤖 智能助手</div>
                <div class="chat-close" onclick="closeAIChat()">✕</div>
            </div>
            <div class="chat-content">
                <div class="chat-message bot">
                    <div class="message-text">您好！我是合肥文旅智能助手，有什么可以帮您的吗？</div>
                </div>
                <div class="common-questions">
                    <div class="question-title">常见问题：</div>
                    <div class="question-item" onclick="askQuestion('合肥景点开放时间？')">合肥景点开放时间？</div>
                    <div class="question-item" onclick="askQuestion('包公园怎么去？')">包公园怎么去？</div>
                    <div class="question-item" onclick="askQuestion('有什么好吃的推荐？')">有什么好吃的推荐？</div>
                </div>
            </div>
            <div class="chat-input">
                <input type="text" placeholder="请输入您的问题..." id="chatInput">
                <button onclick="sendMessage()">发送</button>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="switchTab('explore')">
                <div class="nav-icon">🔍</div>
                <div class="nav-text">探索</div>
            </div>
            <div class="nav-item" onclick="switchTab('community')">
                <div class="nav-icon">👥</div>
                <div class="nav-text">社区</div>
            </div>
            <div class="nav-item" onclick="switchTab('trip')">
                <div class="nav-icon">📅</div>
                <div class="nav-text">行程</div>
            </div>
            <div class="nav-item" onclick="switchTab('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-text">我的</div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.banner-slide');
        const indicators = document.querySelectorAll('.indicator');

        // Banner轮播功能
        function showSlide(index) {
            slides[currentSlide].classList.remove('active');
            indicators[currentSlide].classList.remove('active');
            
            currentSlide = index;
            
            slides[currentSlide].classList.add('active');
            indicators[currentSlide].classList.add('active');
        }

        // 自动轮播
        setInterval(() => {
            const nextSlide = (currentSlide + 1) % slides.length;
            showSlide(nextSlide);
        }, 5000);

        // 交互功能
        function viewAttraction(id) {
            alert(`正在跳转到${id}景点详情页...`);
        }

        function showAttractionInfo(type, attractions) {
            alert(`${type}景点：${attractions}`);
        }

        function planTrip() {
            alert('正在跳转到行程规划页面...');
        }

        function recognizeImage() {
            alert('请选择图片进行识别...');
        }

        function showFilters() {
            alert('筛选选项：免费景点、含停车场、适合亲子等');
        }

        function openAIChat() {
            alert('AI助手：您好！我是合肥文旅智能助手，有什么可以帮您的吗？');
        }

        function switchTab(tab) {
            // 移除所有active状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加active状态到点击的tab
            event.currentTarget.classList.add('active');
            
            alert(`正在切换到${tab}页面...`);
        }

        // 地图点击交互
        document.querySelectorAll('.map-point').forEach(point => {
            point.addEventListener('click', function() {
                this.style.transform = 'scale(1.3)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            });
        });

        // 收藏功能
        function toggleFavorite(event, id) {
            event.stopPropagation();
            const btn = event.target;
            if (btn.classList.contains('active')) {
                btn.classList.remove('active');
                btn.textContent = '♡';
                showToast('已取消收藏');
            } else {
                btn.classList.add('active');
                btn.textContent = '♥';
                showToast('已添加到收藏');
            }
        }

        // 显示提示信息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 20px;
                font-size: 14px;
                z-index: 9999;
                transition: opacity 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        // 查看所有活动
        function viewAllActivities() {
            alert('正在跳转到活动列表页面...');
        }

        // 查看活动详情
        function viewActivity(id) {
            alert(`正在查看${id}活动详情...`);
        }

        // 查看攻略详情
        function viewGuide(id) {
            alert(`正在查看${id}攻略详情...`);
        }

        // AI聊天功能
        function openAIChat() {
            document.getElementById('aiChatModal').classList.add('active');
        }

        function closeAIChat() {
            document.getElementById('aiChatModal').classList.remove('active');
        }

        function askQuestion(question) {
            const chatContent = document.querySelector('.chat-content');

            // 添加用户问题
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-message user';
            userMessage.innerHTML = `<div class="message-text" style="background: #F18F01; color: white; padding: 12px; border-radius: 12px; margin-left: 40px;">${question}</div>`;
            chatContent.appendChild(userMessage);

            // 模拟AI回答
            setTimeout(() => {
                const botMessage = document.createElement('div');
                botMessage.className = 'chat-message bot';
                let answer = '';

                switch(question) {
                    case '合肥景点开放时间？':
                        answer = '大部分景点开放时间为8:00-18:00，包公园全天开放，科技馆周一闭馆。具体时间建议提前查询。';
                        break;
                    case '包公园怎么去？':
                        answer = '包公园位于合肥市包河区芜湖路，可乘坐地铁1号线到包公园站，或乘坐公交6、11、226路等。';
                        break;
                    case '有什么好吃的推荐？':
                        answer = '推荐徽菜：红烧臭鳜鱼、毛豆腐；小吃：庐州烤鸭、合肥龙虾；甜品：桂花糖藕。';
                        break;
                    default:
                        answer = '感谢您的提问，我会为您查找相关信息...';
                }

                botMessage.innerHTML = `<div class="message-text">${answer}</div>`;
                chatContent.appendChild(botMessage);
                chatContent.scrollTop = chatContent.scrollHeight;
            }, 1000);

            chatContent.scrollTop = chatContent.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (message) {
                askQuestion(message);
                input.value = '';
            }
        }

        // 回车发送消息
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加页面滚动效果
            let lastScrollTop = 0;
            const header = document.querySelector('.header');

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // 向下滚动，隐藏header
                    header.style.transform = 'translateY(-100%)';
                } else {
                    // 向上滚动，显示header
                    header.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop;
            });

            // 添加header过渡效果
            header.style.transition = 'transform 0.3s ease';
        });

        // 便民服务功能
        function openService(type) {
            let message = '';
            switch(type) {
                case 'parking':
                    message = '正在为您查找附近停车场...\n\n📍 包公园停车场 - 距离200m - 免费\n📍 科技馆地下停车场 - 距离500m - ¥5/小时';
                    break;
                case 'toilet':
                    message = '正在为您导航到最近的卫生间...\n\n🚻 包公园东门卫生间 - 距离150m\n🚻 科技馆一楼卫生间 - 距离300m';
                    break;
                case 'medical':
                    message = '医疗服务信息：\n\n🏥 合肥市第一人民医院 - 距离2.1km\n🚑 急救电话：120\n💊 附近药店：康美大药房';
                    break;
                case 'lost':
                    message = '失物招领服务：\n\n📞 服务热线：0551-12345\n📍 失物招领处：各景区游客服务中心\n⏰ 服务时间：8:00-18:00';
                    break;
            }
            alert(message);
        }

        // 页面跳转功能
        function openPage(page) {
            switch(page) {
                case 'about':
                    alert('关于我们：合肥市智慧文旅助手致力于为游客提供便捷的旅游服务...');
                    break;
                case 'contact':
                    alert('联系我们：\n📞 客服热线：0551-12345\n📧 邮箱：<EMAIL>\n🕐 服务时间：9:00-21:00');
                    break;
                case 'feedback':
                    alert('感谢您的反馈！请通过以下方式联系我们：\n📱 微信公众号：合肥文旅\n📞 投诉建议：0551-12345');
                    break;
            }
        }

        // 悬浮按钮功能
        let fabOpen = false;

        function toggleFAB() {
            const fabMain = document.querySelector('.fab-main');
            const fabOptions = document.getElementById('fabOptions');
            const fabIcon = document.getElementById('fabIcon');

            fabOpen = !fabOpen;

            if (fabOpen) {
                fabMain.classList.add('active');
                fabOptions.classList.add('active');
                fabIcon.textContent = '×';
            } else {
                fabMain.classList.remove('active');
                fabOptions.classList.remove('active');
                fabIcon.textContent = '+';
            }
        }

        function quickAction(action) {
            let message = '';
            switch(action) {
                case 'emergency':
                    message = '🆘 紧急求助\n\n🚨 报警：110\n🚑 急救：120\n🚒 火警：119\n\n📍 您的位置已发送给相关部门';
                    break;
                case 'translate':
                    message = '🌐 语言翻译服务\n\n支持语言：\n🇺🇸 English\n🇯🇵 日本語\n🇰🇷 한국어\n🇫🇷 Français\n\n请选择需要翻译的语言...';
                    break;
                case 'accessibility':
                    message = '♿ 无障碍服务\n\n🦽 轮椅租借点\n🔊 语音导览\n👁️ 盲道指引\n🤟 手语服务\n\n正在为您提供无障碍路线规划...';
                    break;
            }
            alert(message);
            toggleFAB(); // 关闭FAB菜单
        }

        // 点击页面其他地方关闭FAB菜单
        document.addEventListener('click', function(e) {
            const fabContainer = document.querySelector('.fab-container');
            if (fabOpen && !fabContainer.contains(e.target)) {
                toggleFAB();
            }
        });

        // 模拟实时数据更新
        function updateWeatherInfo() {
            const temps = ['26°C', '27°C', '28°C', '29°C', '30°C'];
            const conditions = ['晴朗', '多云', '微风', '晴转多云'];
            const tips = [
                ['🧴 建议携带防晒霜', '💧 记得多喝水'],
                ['🧥 建议携带薄外套', '☂️ 可能有小雨'],
                ['👕 适合穿轻薄衣物', '🕶️ 紫外线较强'],
                ['🌤️ 天气舒适', '📷 适合拍照']
            ];

            const randomIndex = Math.floor(Math.random() * temps.length);

            document.querySelector('.weather-temp').textContent = temps[randomIndex];
            document.querySelector('.weather-desc').textContent = conditions[randomIndex] + ' · 适宜出游';

            const tipItems = document.querySelectorAll('.tip-item');
            tips[randomIndex].forEach((tip, index) => {
                if (tipItems[index]) {
                    tipItems[index].textContent = tip;
                }
            });
        }

        // 每30秒更新一次天气信息
        setInterval(updateWeatherInfo, 30000);
    </script>
</body>
</html>
