<template>
  <div class="index-container">
    <!-- 顶部导航栏 -->
    <header class="header" :class="{ 'header-mobile': isMobile }">
      <div class="header-content">
        <div class="logo">
          <img src="/logo.png" alt="Logo" class="logo-img" />
          <span class="logo-text">智慧平台</span>
        </div>
        
        <!-- 桌面端导航 -->
        <nav class="nav-desktop" v-if="!isMobile">
          <a href="#home" class="nav-link active">首页</a>
          <a href="#features" class="nav-link">功能</a>
          <a href="#about" class="nav-link">关于</a>
          <a href="#contact" class="nav-link">联系</a>
        </nav>
        
        <!-- 移动端菜单按钮 -->
        <button 
          class="menu-toggle" 
          v-if="isMobile"
          @click="toggleMobileMenu"
          :class="{ 'active': showMobileMenu }"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
      
      <!-- 移动端导航菜单 -->
      <nav class="nav-mobile" v-if="isMobile && showMobileMenu">
        <a href="#home" class="nav-link" @click="closeMobileMenu">首页</a>
        <a href="#features" class="nav-link" @click="closeMobileMenu">功能</a>
        <a href="#about" class="nav-link" @click="closeMobileMenu">关于</a>
        <a href="#contact" class="nav-link" @click="closeMobileMenu">联系</a>
      </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- Hero区域 -->
      <section class="hero-section" id="home">
        <div class="hero-content" :class="{ 'hero-mobile': isMobile }">
          <div class="hero-text">
            <h1 class="hero-title">
              欢迎使用智慧平台
            </h1>
            <p class="hero-subtitle">
              为您提供高效、便捷的数字化解决方案
            </p>
            <div class="hero-buttons">
              <button class="btn btn-primary" @click="handleGetStarted">
                立即开始
              </button>
              <button class="btn btn-secondary" @click="handleLearnMore">
                了解更多
              </button>
            </div>
          </div>
          <div class="hero-image" v-if="!isMobile">
            <img src="/hero-image.png" alt="Hero Image" />
          </div>
        </div>
      </section>

      <!-- 功能特色区域 -->
      <section class="features-section" id="features">
        <div class="section-container">
          <h2 class="section-title">核心功能</h2>
          <div class="features-grid" :class="{ 'features-mobile': isMobile }">
            <div 
              class="feature-card" 
              v-for="feature in features" 
              :key="feature.id"
              @click="handleFeatureClick(feature)"
            >
              <div class="feature-icon">
                <i :class="feature.icon"></i>
              </div>
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 统计数据区域 -->
      <section class="stats-section">
        <div class="section-container">
          <div class="stats-grid" :class="{ 'stats-mobile': isMobile }">
            <div class="stat-item" v-for="stat in stats" :key="stat.id">
              <div class="stat-number">{{ stat.number }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 关于我们区域 -->
      <section class="about-section" id="about">
        <div class="section-container">
          <div class="about-content" :class="{ 'about-mobile': isMobile }">
            <div class="about-text">
              <h2 class="section-title">关于我们</h2>
              <p class="about-description">
                我们致力于为用户提供最优质的数字化服务体验，
                通过创新技术和用户至上的理念，打造行业领先的智慧平台。
              </p>
              <ul class="about-features">
                <li>专业的技术团队</li>
                <li>7x24小时服务支持</li>
                <li>安全可靠的数据保护</li>
                <li>持续的产品创新</li>
              </ul>
            </div>
            <div class="about-image" v-if="!isMobile">
              <img src="/about-image.png" alt="About Us" />
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 底部区域 -->
    <footer class="footer" :class="{ 'footer-mobile': isMobile }">
      <div class="footer-content">
        <div class="footer-section">
          <h4>联系我们</h4>
          <p>邮箱: <EMAIL></p>
          <p>电话: ************</p>
        </div>
        <div class="footer-section" v-if="!isMobile">
          <h4>快速链接</h4>
          <a href="#home">首页</a>
          <a href="#features">功能</a>
          <a href="#about">关于</a>
        </div>
        <div class="footer-section" v-if="!isMobile">
          <h4>关注我们</h4>
          <div class="social-links">
            <a href="#" class="social-link">微信</a>
            <a href="#" class="social-link">微博</a>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2024 智慧平台. 保留所有权利.</p>
      </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button 
      class="back-to-top" 
      v-show="showBackToTop"
      @click="scrollToTop"
    >
      <i class="fas fa-arrow-up"></i>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 响应式数据
const isMobile = ref(false)
const showMobileMenu = ref(false)
const showBackToTop = ref(false)

// 功能数据
const features = ref([
  {
    id: 1,
    icon: 'fas fa-rocket',
    title: '高效便捷',
    description: '简化操作流程，提升工作效率'
  },
  {
    id: 2,
    icon: 'fas fa-shield-alt',
    title: '安全可靠',
    description: '多重安全保障，保护数据安全'
  },
  {
    id: 3,
    icon: 'fas fa-cogs',
    title: '智能化',
    description: 'AI驱动的智能化解决方案'
  },
  {
    id: 4,
    icon: 'fas fa-users',
    title: '团队协作',
    description: '支持多人协作，提升团队效率'
  }
])

// 统计数据
const stats = ref([
  { id: 1, number: '10,000+', label: '活跃用户' },
  { id: 2, number: '99.9%', label: '系统稳定性' },
  { id: 3, number: '24/7', label: '技术支持' },
  { id: 4, number: '50+', label: '企业客户' }
])

// 检测设备类型
const checkDevice = () => {
  isMobile.value = window.innerWidth <= 768
}

// 处理窗口大小变化
const handleResize = () => {
  checkDevice()
  if (!isMobile.value) {
    showMobileMenu.value = false
  }
}

// 处理滚动事件
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 返回顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 事件处理函数
const handleGetStarted = () => {
  console.log('开始使用')
  // 这里可以添加路由跳转或其他逻辑
}

const handleLearnMore = () => {
  console.log('了解更多')
  // 这里可以添加路由跳转或其他逻辑
}

const handleFeatureClick = (feature) => {
  console.log('点击功能:', feature.title)
  // 这里可以添加功能详情页面跳转
}

// 生命周期
onMounted(() => {
  checkDevice()
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.index-container {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e0e0e0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.header-mobile .header-content {
  height: 60px;
  padding: 0 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: bold;
  color: #2c3e50;
}

.nav-desktop {
  display: flex;
  gap: 32px;
}

.nav-link {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #3498db;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 2px;
  background: #3498db;
}

/* 移动端菜单按钮 */
.menu-toggle {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.menu-toggle span {
  width: 100%;
  height: 3px;
  background: #333;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(8px, 8px);
}

.menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 移动端导航菜单 */
.nav-mobile {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 主要内容区域 */
.main-content {
  margin-top: 70px;
}

/* Hero区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  min-height: 600px;
  display: flex;
  align-items: center;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-mobile {
  grid-template-columns: 1fr;
  text-align: center;
  gap: 40px;
  padding: 0 16px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 14px 28px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #667eea;
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-content {
    margin-top: 60px;
  }

  .hero-section {
    padding: 60px 0;
    min-height: 500px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .btn {
    padding: 12px 24px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 280px;
  }
}

/* 通用区域样式 */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 60px;
  color: #2c3e50;
}

/* 功能特色区域 */
.features-section {
  padding: 100px 0;
  background: #f8f9fa;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.features-mobile {
  grid-template-columns: 1fr;
  gap: 30px;
}

.feature-card {
  background: white;
  padding: 40px 30px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 32px;
  color: white;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 16px;
  color: #2c3e50;
}

.feature-description {
  color: #666;
  line-height: 1.6;
}

/* 统计数据区域 */
.stats-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
}

.stats-mobile {
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 关于我们区域 */
.about-section {
  padding: 100px 0;
  background: white;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.about-mobile {
  grid-template-columns: 1fr;
  gap: 40px;
}

.about-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.8;
}

.about-features {
  list-style: none;
}

.about-features li {
  padding: 8px 0;
  position: relative;
  padding-left: 24px;
  color: #555;
}

.about-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #3498db;
  font-weight: bold;
}

.about-image img {
  width: 100%;
  height: auto;
  border-radius: 12px;
}

/* 底部区域 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 60px 0 20px;
}

.footer-mobile {
  padding: 40px 0 20px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.footer-section h4 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: #ecf0f1;
}

.footer-section p,
.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  margin-bottom: 8px;
  display: block;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #3498db;
}

.social-links {
  display: flex;
  gap: 16px;
}

.social-link {
  padding: 8px 16px;
  background: #34495e;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.social-link:hover {
  background: #3498db;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  margin-top: 40px;
  padding-top: 20px;
  text-align: center;
  color: #95a5a6;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  z-index: 999;
}

.back-to-top:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
  .section-container {
    padding: 0 16px;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 40px;
  }

  .features-section,
  .about-section {
    padding: 60px 0;
  }

  .stats-section {
    padding: 60px 0;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .feature-title {
    font-size: 1.25rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .about-description {
    font-size: 1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 0 16px;
  }

  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
}

@media (max-width: 480px) {
  .stats-mobile {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2rem;
  }

  .social-links {
    flex-direction: column;
  }
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card,
.stat-item,
.about-content {
  animation: fadeInUp 0.6s ease forwards;
}
</style>
